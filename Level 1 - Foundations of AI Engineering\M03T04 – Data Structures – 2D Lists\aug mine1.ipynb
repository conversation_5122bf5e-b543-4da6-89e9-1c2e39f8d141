def minesweeper(grid):
    """
    Takes a grid of # (mines) and - (empty spots) and returns a grid where
    each - is replaced by the number of adjacent mines.

    Args:
        grid: 2D list containing '#' for mines and '-' for empty spots

    Returns:
        2D list with mines as '#' and empty spots as numbers indicating adjacent mine count
    """
    # Get grid dimensions
    rows = len(grid)
    cols = len(grid[0]) if rows > 0 else 0

    # Create a new grid to store results
    result = []

    # Helper function to check if a position is valid (within bounds)
    def is_valid_position(row, col):
        return 0 <= row < rows and 0 <= col < cols

    # Helper function to count adjacent mines for a given position
    def count_adjacent_mines(row, col):
        count = 0

        # Check all 8 adjacent positions
        # Using the direction table from the hint:
        directions = [
            (-1, -1),  # NW (Northwest)
            (-1,  0),  # N  (North)
            (-1,  1),  # NE (Northeast)
            ( 0, -1),  # W  (West)
            ( 0,  1),  # E  (East)
            ( 1, -1),  # SW (Southwest)
            ( 1,  0),  # S  (South)
            ( 1,  1)   # SE (Southeast)
        ]

        for dr, dc in directions:
            adjacent_row = row + dr
            adjacent_col = col + dc

            # Check if the adjacent position is valid and contains a mine
            if is_valid_position(adjacent_row, adjacent_col):
                if grid[adjacent_row][adjacent_col] == '#':
                    count += 1

        return count

    # Process each row
    for row_index, row in enumerate(grid):
        new_row = []

        # Process each cell in the row
        for col_index, cell in enumerate(row):
            if cell == '#':
                # If it's a mine, keep it as a mine
                new_row.append('#')
            else:
                # If it's empty, count adjacent mines
                mine_count = count_adjacent_mines(row_index, col_index)
                new_row.append(mine_count)

        result.append(new_row)

    return result


# Test the function with the provided example
def test_minesweeper():
    """Test function to verify our minesweeper implementation"""

    # Input grid from the example
    test_grid = [
        ["-", "-", "-", "#", "#"],
        ["-", "#", "-", "-", "-"],
        ["-", "-", "#", "-", "-"],
        ["-", "#", "#", "-", "-"],
        ["-", "-", "-", "-", "-"]
    ]

    # Expected output from the example
    expected_output = [
        [1, 1, 2, "#", "#"],
        [1, "#", 3, 3, 2],
        [2, 4, "#", 2, 0],
        [1, "#", "#", 2, 0],
        [1, 2, 2, 1, 0]
    ]

    # Run our function
    result = minesweeper(test_grid)

    # Print the results
    print("Input grid:")
    for row in test_grid:
        print(row)

    print("\nOutput grid:")
    for row in result:
        print(row)

    print("\nExpected output:")
    for row in expected_output:
        print(row)

    # Check if our result matches the expected output
    if result == expected_output:
        print("\n✅ Test passed! The function works correctly.")
    else:
        print("\n❌ Test failed. Let's check the differences:")
        for i, (actual_row, expected_row) in enumerate(zip(result, expected_output)):
            if actual_row != expected_row:
                print(f"Row {i}: Got {actual_row}, Expected {expected_row}")


# Run the test when the script is executed
if __name__ == "__main__":
    test_minesweeper()