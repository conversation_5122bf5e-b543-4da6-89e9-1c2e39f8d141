def minesweeper(grid):
    """
    Takes a grid with '#' (mines) and '-' (empty spots).
    Returns a grid where each '-' is replaced with the count of adjacent mines.
    """
    rows = len(grid)
    cols = len(grid[0])

    # All 8 directions around a cell
    directions = [
        (-1, -1), (-1, 0), (-1, 1),  # Top row
        ( 0, -1),          ( 0, 1),  # Middle row (skip center)
        ( 1, -1), ( 1, 0), ( 1, 1)   # Bottom row
    ]

    result = []

    # Process each row using enumerate (gives us both index and value)
    for row_idx, row_data in enumerate(grid):
        new_row = []

        # Process each cell using enumerate
        for col_idx, cell_value in enumerate(row_data):
            if cell_value == '#':
                # Keep mines as mines
                new_row.append('#')
            else:
                # Count adjacent mines
                mine_count = 0

                # Check all 8 directions
                for dr, dc in directions:
                    new_row_pos = row_idx + dr
                    new_col_pos = col_idx + dc

                    # Check if position is valid and contains a mine
                    if (0 <= new_row_pos < rows and
                        0 <= new_col_pos < cols and
                        grid[new_row_pos][new_col_pos] == '#'):
                        mine_count += 1

                new_row.append(mine_count)

        result.append(new_row)

    return result


# Test the function
if __name__ == "__main__":
    test_grid = [
        ["-", "-", "-", "#", "#"],
        ["-", "#", "-", "-", "-"],
        ["-", "-", "#", "-", "-"],
        ["-", "#", "#", "-", "-"],
        ["-", "-", "-", "-", "-"]
    ]

    output = minesweeper(test_grid)

    print("Input grid:")
    for row in test_grid:
        print(row)

    print("\nOutput grid:")
    for row in output:
        print(row)