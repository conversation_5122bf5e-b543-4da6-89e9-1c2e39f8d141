"""
Minesweeper Solution - Step by Step Explanation
This version shows the thinking process more clearly for learning purposes.
"""

def minesweeper_simple(grid):
    """
    Simple version with clear step-by-step logic
    """
    # Step 1: Get the size of our grid
    num_rows = len(grid)
    num_cols = len(grid[0])
    
    # Step 2: Create an empty result grid
    result = []
    
    # Step 3: Go through each row
    for row_index in range(num_rows):
        new_row = []  # Create a new row for our result
        
        # Step 4: Go through each column in this row
        for col_index in range(num_cols):
            current_cell = grid[row_index][col_index]
            
            # Step 5: If it's a mine, keep it as a mine
            if current_cell == '#':
                new_row.append('#')
            else:
                # Step 6: If it's empty, count nearby mines
                mine_count = 0
                
                # Check all 8 directions around this cell
                # We'll check: top-left, top, top-right, left, right, bottom-left, bottom, bottom-right
                
                for row_offset in [-1, 0, 1]:  # -1 = above, 0 = same row, 1 = below
                    for col_offset in [-1, 0, 1]:  # -1 = left, 0 = same col, 1 = right
                        
                        # Skip the current cell itself (when both offsets are 0)
                        if row_offset == 0 and col_offset == 0:
                            continue
                        
                        # Calculate the position we want to check
                        check_row = row_index + row_offset
                        check_col = col_index + col_offset
                        
                        # Make sure we don't go outside the grid boundaries
                        if (0 <= check_row < num_rows and 0 <= check_col < num_cols):
                            # If there's a mine at this position, count it
                            if grid[check_row][check_col] == '#':
                                mine_count += 1
                
                # Add the count to our result
                new_row.append(mine_count)
        
        # Add this completed row to our result
        result.append(new_row)
    
    return result


def demonstrate_step_by_step():
    """
    Shows how the algorithm works step by step
    """
    # Small example to demonstrate
    small_grid = [
        ["-", "#", "-"],
        ["-", "-", "-"],
        ["#", "-", "-"]
    ]
    
    print("Let's trace through a small 3x3 grid:")
    print("Input:")
    for row in small_grid:
        print(row)
    
    print("\nStep by step process:")
    
    # Manual walkthrough for position [0,0] (top-left)
    print("\nFor position [0,0] (top-left corner with value '-'):")
    print("We check these adjacent positions:")
    print("  - [0,1] = '#' (mine!) -> count = 1")
    print("  - [1,0] = '-' (safe)")
    print("  - [1,1] = '-' (safe)")
    print("  So position [0,0] gets value 1")
    
    print("\nFor position [0,1] (top-middle with value '#'):")
    print("  This is a mine, so it stays as '#'")
    
    print("\nFor position [1,1] (center with value '-'):")
    print("We check all 8 adjacent positions:")
    print("  - [0,0] = '-' (safe)")
    print("  - [0,1] = '#' (mine!) -> count = 1")
    print("  - [0,2] = '-' (safe)")
    print("  - [1,0] = '-' (safe)")
    print("  - [1,2] = '-' (safe)")
    print("  - [2,0] = '#' (mine!) -> count = 2")
    print("  - [2,1] = '-' (safe)")
    print("  - [2,2] = '-' (safe)")
    print("  So position [1,1] gets value 2")
    
    result = minesweeper_simple(small_grid)
    print("\nFinal result:")
    for row in result:
        print(row)


if __name__ == "__main__":
    demonstrate_step_by_step()
    
    print("\n" + "="*50)
    print("Testing with the original example:")
    
    # Test with the original example
    test_grid = [
        ["-", "-", "-", "#", "#"],
        ["-", "#", "-", "-", "-"],
        ["-", "-", "#", "-", "-"],
        ["-", "#", "#", "-", "-"],
        ["-", "-", "-", "-", "-"]
    ]
    
    result = minesweeper_simple(test_grid)
    
    print("Result:")
    for row in result:
        print(row)
