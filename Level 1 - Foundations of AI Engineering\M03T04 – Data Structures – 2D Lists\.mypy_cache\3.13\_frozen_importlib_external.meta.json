{"data_mtime": 1757979915, "dep_lines": [3, 4, 8, 9, 11, 16, 1, 2, 3, 5, 6, 7, 12, 13, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [10, 5, 5, 5, 5, 10, 10, 10, 20, 10, 10, 5, 5, 5, 5, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["importlib.abc", "importlib.machinery", "_typeshed.importlib", "collections.abc", "importlib.metadata", "importlib.readers", "_ast", "_io", "importlib", "sys", "types", "_typeshed", "typing", "typing_extensions", "builtins", "_collections_abc", "_frozen_importlib", "abc", "ast", "importlib._abc", "os", "warnings"], "hash": "f10b55cab201631dcc95f81f2303997322e740c8", "id": "_frozen_importlib_external", "ignore_all": true, "interface_hash": "9104a525a5e7d06d55a06e5d6272fbc4918d7762", "mtime": 1756068577, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "c:\\Users\\<USER>\\.vscode\\extensions\\ms-python.mypy-type-checker-2025.2.0\\bundled\\libs\\mypy\\typeshed\\stdlib\\_frozen_importlib_external.pyi", "plugin_data": null, "size": 8117, "suppressed": [], "version_id": "1.15.0"}